.statistical-analysis {
  width: 100%;
  height: 100%;
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* 筛选与导出区（顶部） */
.filter-export-section {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;

  :global {
    .ant-select-selection-item {
      font-size: 12px;
    }
  }

  /* 左侧：筛选控件组 */
  .filter-controls {
    display: flex;
    align-items: center;
    gap: 24px;

    .time-dimension,
    .period-selection {
      display: flex;
      align-items: center;
      gap: 12px;

      .label {
        font-size: 12px;
        color: #262626;
        font-weight: 500;
        white-space: nowrap;
      }
    }
  }

  /* 右侧：导出按钮 */
  .export-button {
    padding: 8px 16px;
    background: #1890ff;
    color: #fff;
    border: 1px solid #1890ff;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: normal;

    &:hover {
      background: #40a9ff;
      border-color: #40a9ff;
    }
  }
}

/* 关键指标展示区（卡片式） */
.key-indicators-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;

  .indicator-card {
    background: #fff;
    padding: 10px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    position: relative;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    /* 右侧图标 */
    .card-icon {
      position: absolute;
      top: 12px;
      right: 12px;
      opacity: 0.7;
    }

    /* 最上层：核心数值 */
    .card-value {
      margin-top: 4px;
      margin-bottom: 5px;

      .value-number {
        font-size: 24px;
        font-weight: 600;
        color: #116aa5;
        line-height: 1;
      }

      .value-unit {
        font-size: 12px;
        color: #777b7e;
        margin-left: 4px;
        font-weight: normal;
      }
    }

    /* 中间层：指标定义 + 交互提示 */
    .card-title {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-bottom: 4px;

      .title-text {
        font-size: 12px;
        color: #595959;
        font-weight: 500;
        line-height: 1.3;
      }

      .title-tooltip {
        width: 14px;
        height: 14px;
        border-radius: 50%;
        background: #f0f0f0;
        color: #8c8c8c;
        font-size: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: help;
        flex-shrink: 0;

        &:hover {
          background: #e6f7ff;
          color: #1890ff;
        }
      }
    }

    /* 最下层：趋势对比 */
    .card-trend {
      margin-top: auto;

      .trend-up,
      .trend-down {
        display: flex;
        align-items: center;
        gap: 3px;
        font-size: 11px;

        .trend-arrow {
          font-size: 12px;
        }
      }

      .trend-up {
        color: #52c41a;

        .trend-arrow {
          color: #52c41a;
        }
      }

      .trend-down {
        color: #f0292c;

        .trend-arrow {
          color: #f0292c;
        }
      }
    }
  }
}

/* 数据可视化区 */
.visualization-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;

  .chart-row {
    display: flex;
    gap: 16px;
    height: 400px;

    /* 第一排：能耗趋势占2/3，单品能耗占1/3 */
    &:first-child {
      .chart-container:first-child {
        flex: 2; /* 能耗趋势占2/3 */
      }
      .chart-container:last-child {
        flex: 1; /* 单品能耗占1/3 */
      }
    }

    /* 第二排：三个图表平均分配 */
    &:last-child {
      .chart-container {
        flex: 1;
      }
    }

    .chart-container {
      display: flex;
      flex-direction: column;

      .chart-title {
        font-size: 14px;
        font-weight: 600;
        color: #262626;
        padding-bottom: 8px;
      }

      .chart-content {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }
}

/* 统一的图表组件样式 */
.energy-trend-chart,
.product-energy-chart,
.product-ranking-chart,
.energy-structure-chart,
.energy-composition-chart {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  padding: 8px;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1; // 自适应父容器

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f5f5f5;

    .chart-title {
      font-size: 14px;
      font-weight: 600;
      color: #262626;
      line-height: 1.4;
    }

    .chart-controls {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .chart-more {
      font-size: 12px;
      color: #1890ff;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        background: #f0f8ff;
        color: #096dd9;
      }

      &:active {
        background: #e6f7ff;
      }
    }
  }

  .chart-container {
    flex: 1;
    min-height: 200px;
    width: 100%;
  }
}

/* Modal样式 */
.chart-modal {
  .modal-placeholder {
    text-align: center;
    padding: 40px 20px;
    color: #8c8c8c;

    p {
      margin-bottom: 8px;
      line-height: 1.6;

      &:first-child {
        font-size: 16px;
        font-weight: 500;
        color: #595959;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .energy-trend-chart,
  .product-energy-chart,
  .product-ranking-chart,
  .energy-structure-chart,
  .energy-composition-chart {
    padding: 12px;

    .chart-header {
      margin-bottom: 12px;

      .chart-title {
        font-size: 13px;
      }

      .chart-more {
        font-size: 11px;
        padding: 3px 6px;
      }
    }

    .chart-container {
      min-height: 160px;
    }
  }
}
